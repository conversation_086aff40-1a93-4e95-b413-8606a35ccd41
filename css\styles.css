/* COAST Application Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}


.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background-color: #2c3e50;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 0.5rem;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #2c3e50;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.back-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #666;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #2c3e50;
}

.back-link::before {
    content: '←';
    margin-right: 0.5rem;
}

/* Main Content Styles */
.main-content {
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Login Form Styles */
.login-container {
    max-width: 400px;
    margin: 4rem auto;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 2px rgba(44, 62, 80, 0.1);
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    color: white;
    width: 100%;
}

.btn-primary:hover {
    background-color: #34495e;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
    width: 100%;
}

.btn-secondary:hover {
    background-color: #e9ecef;
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.forgot-password {
    text-align: center;
    margin-top: 1rem;
}

.forgot-password a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password a:hover {
    text-decoration: underline;
}

/* Footer Styles */
.footer {
    background-color: #fff;
    border-top: 1px solid #e9ecef;
    padding: 2rem 0;
    margin-top: auto;
}

/* .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
} */

.footer-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2c3e50;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.dashboard-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    margin-bottom: 4rem;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 2rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-actions .btn {
    min-width: 150px;
}

/* Features Section */
.features {
    margin-bottom: 4rem;
}

.features h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 3rem;
}

/* Dashboard Specific Styles */
.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: #666;
    font-size: 1.1rem;
}

.card-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

.security-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.status-indicator {
    font-size: 1.2rem;
}

.status-good {
    color: #28a745;
}

.status-warning {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

.recent-activity {
    margin-top: 3rem;
}

.recent-activity h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.activity-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
    min-width: 100px;
}

.activity-description {
    flex: 1;
    margin-left: 1rem;
}

/* User Menu Styles */
.user-greeting {
    color: #666;
    margin-right: 1rem;
}

/* Form Error Styles */
.form-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.field-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Message Styles */
.message {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.message-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Loading Styles */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

/* Back Link Styles */
.back-link-container {
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .container {
        padding: 0 1rem;
    }

    .login-container {
        margin: 2rem auto;
        padding: 1.5rem;
    }

    .footer-container {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .card-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-time {
        min-width: auto;
    }

    .activity-description {
        margin-left: 0;
    }
}
