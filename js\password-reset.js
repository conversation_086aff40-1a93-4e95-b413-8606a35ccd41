// Password reset page functionality
document.addEventListener('DOMContentLoaded', function() {
    const resetForm = document.getElementById('resetForm');

    // Handle password reset form submission
    if (resetForm) {
        resetForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (!UIHelpers.validateForm('resetForm')) {
                UIHelpers.showMessage('Please enter a valid email address.', 'error');
                return;
            }

            // Get form data
            const formData = new FormData(resetForm);
            const email = formData.get('email');

            // Validate email format
            if (!isValidEmail(email)) {
                UIHelpers.showMessage('Please enter a valid email address.', 'error');
                return;
            }

            // Show loading state
            const submitButton = resetForm.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Sending...';
            submitButton.disabled = true;

            // Simulate password reset process
            setTimeout(() => {
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;

                // Show success message
                UIHelpers.showMessage('Password reset link sent! Check your email.', 'success');
                
                // Clear the form
                resetForm.reset();
                
                // In a real application, you might redirect back to login after a delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);
                
            }, 2000);
        });
    }

    // Add input validation feedback
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            if (this.value && !isValidEmail(this.value)) {
                this.classList.add('error');
                showFieldError(this, 'Please enter a valid email address.');
            } else {
                this.classList.remove('error');
                hideFieldError(this);
            }
        });

        emailInput.addEventListener('input', function() {
            if (this.classList.contains('error') && isValidEmail(this.value)) {
                this.classList.remove('error');
                hideFieldError(this);
            }
        });
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showFieldError(field, message) {
    // Remove existing error message
    hideFieldError(field);
    
    // Create and show new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function hideFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}
