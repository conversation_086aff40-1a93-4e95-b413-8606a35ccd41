<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in to COAST</title>
    <link rel="stylesheet" href="css/header.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <style>
        /* Login page specific header adjustments */
        .header {
            flex-shrink: 0; /* Prevent header from shrinking */
        }
        .header-container {
            padding: 0.75rem 2rem; /* Reduced padding for compact header */
        }

        /* Page-only footer styles for login page to match provided screenshot */
        .login-footer {
            text-align: center;
            padding: 1rem 0 1.5rem;
            color: #374151;
            flex-shrink: 0; /* Prevent footer from shrinking */
        }
        /* center the inner footer to match login card width */
        .login-footer-inner { max-width: 520px; margin: 0 auto; }
        .login-footer .language {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            color: #0f1724;
            font-size: 0.85rem;
            margin-bottom: 6px;
        }
        .login-footer .language svg { width: 14px; height: 14px; fill: #0b84a8; }
        .login-footer .links { display: inline-flex; gap: 10px; align-items: center; color: #6b7280; }
        .login-footer .links a { color: #6b7280; text-decoration: none; font-size: 0.85rem; }
        .login-footer .links a:not(:last-child)::after { content: '|'; color: #c7cdd6; margin-left: 10px; margin-right: 10px; }
    </style>
</head>
<body>
    
    <header class="header">
    <div class="header-container">
        <a href="index.html" class="logo">
            <!-- <div class="logo-icon">
                <svg width="20" height="20" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8V444.8C394 378 431.1 230.1 432 141.4L256 66.8l0 0z"/>
                </svg>
            </div> -->
            <span class="logo-text">COAST</span>
        </a>
    </div>
</header>


    <!-- Main Login Container -->
    <main class="page-content">
        <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">Sign in to COAST</h1>
            <p class="login-subtitle">Secure login with MFA and SSO available</p>
        </div>

        <form id="loginForm" class="login-form">
            <div class="form-group">
                <label for="email" class="form-label">Email / Username</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    placeholder="Enter your email or username"
                    required
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="form-input"
                    placeholder="Enter your password"
                    required
                >
            </div>

            <button type="submit" class="btn btn-primary">Log In</button>
        </form>

        <div class="divider">Or continue with</div>

        <button type="button" class="btn btn-secondary" id="ssoLogin">
            Log in with SSO
        </button>

        <div class="forgot-password">
            <a href="password_reset.html">Forgot password?</a>
        </div>
        </div>
    </main>

    <!-- Bottom Footer (login page only) -->
    <footer class="login-footer">
        <div class="login-footer-inner">
            <div class="language">
                <span>🌐English</span>
            </div>
            <div class="links">
                <a href="privacy.html">Privacy Policy</a>
                <a href="terms.html">Terms of Service</a>
            </div>
        </div>
    </footer>
</body>
</html>