// Dashboard page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load dashboard data
    loadDashboardData();
});

function initializeDashboard() {
    // Check if user is authenticated (in a real app, this would check actual auth state)
    const isAuthenticated = checkAuthStatus();
    
    if (!isAuthenticated) {
        // Redirect to login if not authenticated
        window.location.href = 'login.html';
        return;
    }
    
    // Update header for authenticated user
    updateHeaderForAuthenticatedUser();
}

function checkAuthStatus() {
    // In a real application, this would check actual authentication status
    // For demo purposes, we'll assume user is authenticated if they came from login
    return document.referrer.includes('login.html') || localStorage.getItem('demo_authenticated') === 'true';
}

function updateHeaderForAuthenticatedUser() {
    // Wait for header to load, then update it
    document.addEventListener('componentLoaded', function(e) {
        if (e.detail.componentPath.includes('header.html')) {
            const userMenu = document.querySelector('.user-menu');
            if (userMenu) {
                userMenu.innerHTML = `
                    <span class="user-greeting">Welcome back!</span>
                    <button class="btn btn-secondary" onclick="logout()">Sign Out</button>
                `;
            }
        }
    });
}

function setupEventListeners() {
    // Generate Report button
    const generateReportBtn = document.querySelector('.dashboard-card button');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', function() {
            generateReport();
        });
    }
    
    // Refresh dashboard data every 30 seconds
    setInterval(loadDashboardData, 30000);
}

function loadDashboardData() {
    // Simulate loading dashboard data
    updateStats();
    updateSecurityStatus();
    updateRecentActivity();
}

function updateStats() {
    // Simulate real-time stats updates
    const statValues = document.querySelectorAll('.stat-value');
    
    if (statValues.length >= 2) {
        // Update user count with some variation
        const currentUsers = parseInt(statValues[0].textContent.replace(',', ''));
        const newUsers = currentUsers + Math.floor(Math.random() * 10) - 5;
        statValues[0].textContent = newUsers.toLocaleString();
        
        // Update uptime
        const uptimes = ['98.5%', '99.1%', '98.9%', '99.3%'];
        statValues[1].textContent = uptimes[Math.floor(Math.random() * uptimes.length)];
    }
}

function updateSecurityStatus() {
    const securityStatus = document.querySelector('.security-status');
    if (securityStatus) {
        // Simulate security status check
        const isSecure = Math.random() > 0.1; // 90% chance of being secure
        
        if (isSecure) {
            securityStatus.innerHTML = `
                <span class="status-indicator status-good">✓</span>
                <span>All systems secure</span>
            `;
        } else {
            securityStatus.innerHTML = `
                <span class="status-indicator status-warning">⚠</span>
                <span>Security scan in progress</span>
            `;
        }
    }
}

function updateRecentActivity() {
    // This would typically fetch real activity data from an API
    const activityList = document.querySelector('.activity-list');
    if (activityList) {
        // For demo purposes, we'll just update timestamps
        const timeElements = activityList.querySelectorAll('.activity-time');
        timeElements.forEach((element, index) => {
            const hoursAgo = (index + 1) * 2;
            element.textContent = `${hoursAgo} hours ago`;
        });
    }
}

function generateReport() {
    const button = event.target;
    const originalText = button.textContent;
    
    // Show loading state
    button.textContent = 'Generating...';
    button.disabled = true;
    
    // Simulate report generation
    setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
        
        UIHelpers.showMessage('Report generated successfully!', 'success');
        
        // In a real app, this would trigger a download or redirect to the report
        console.log('Report would be generated and downloaded here');
    }, 3000);
}

function logout() {
    // Clear authentication state
    localStorage.removeItem('demo_authenticated');
    
    // Show message and redirect
    UIHelpers.showMessage('Signing out...', 'info');
    
    setTimeout(() => {
        window.location.href = 'login.html';
    }, 1000);
}

// Make logout function globally available
window.logout = logout;
