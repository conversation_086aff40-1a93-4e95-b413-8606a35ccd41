// Component loader for modular HTML includes
class ComponentLoader {
    static async loadComponent(elementId, componentPath) {
        try {
            const response = await fetch(componentPath);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${response.status}`);
            }
            const html = await response.text();
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = html;
                // Trigger custom event for component loaded
                element.dispatchEvent(new CustomEvent('componentLoaded', {
                    detail: { componentPath }
                }));
            }
        } catch (error) {
            console.error('Error loading component:', error);
        }
    }

    static async loadAllComponents() {
        const components = [
            { id: 'header-placeholder', path: 'components/header.html' },
            { id: 'footer-placeholder', path: 'components/footer.html' }
        ];

        // Load all components in parallel
        await Promise.all(
            components.map(component => 
                this.loadComponent(component.id, component.path)
            )
        );

        // Initialize navigation after components are loaded
        this.initializeNavigation();
    }

    static initializeNavigation() {
        // Set active navigation link based on current page
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    static init() {
        // Load components when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.loadAllComponents());
        } else {
            this.loadAllComponents();
        }
    }
}

// Auto-initialize when script loads
ComponentLoader.init();

// Utility functions for common UI interactions
class UIHelpers {
    static showLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = '<div class="loading">Loading...</div>';
        }
    }

    static hideLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.remove();
            }
        }
    }

    static showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        
        // Add to top of body
        document.body.insertBefore(messageDiv, document.body.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }

    static validateForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return false;

        const inputs = form.querySelectorAll('input[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
            } else {
                input.classList.remove('error');
            }
        });

        return isValid;
    }
}

// Export for use in other scripts
window.ComponentLoader = ComponentLoader;
window.UIHelpers = UIHelpers;
